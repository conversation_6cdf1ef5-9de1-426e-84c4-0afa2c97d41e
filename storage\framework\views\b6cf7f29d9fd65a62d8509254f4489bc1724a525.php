<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'ระบบจัดการ - ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600;700;800&family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Simple Admin Theme CSS -->
    <link href="<?php echo e(asset('css/simple-admin-theme.css')); ?>" rel="stylesheet">
    <!-- Image Optimization CSS -->
    <link href="<?php echo e(asset('css/image-optimization.css')); ?>" rel="stylesheet">
    <!-- Modern Pagination CSS -->
    <link href="<?php echo e(asset('css/modern-pagination.css')); ?>" rel="stylesheet">
    
    <style>
        /* ========== Simple Admin Theme ========== */
        body {
            font-family: 'Prompt', 'Sarabun', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #212529;
        }

        /* ========== Sidebar - เรียบง่าย ========== */
        .sidebar {
            min-height: 100vh;
            background: #343a40;
            color: #ffffff;
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            overflow-y: auto;
            overflow-x: hidden;
            border-right: 1px solid #495057;
            z-index: 1000;
        }

        /* Sidebar decorations removed */

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 4px;
            margin: 2px 0.5rem;
            font-weight: 400;
            background: transparent;
            border: none;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            min-height: 40px;
        }

        .sidebar .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }

        .sidebar .nav-link.active {
            background: rgba(255, 255, 255, 0.15);
            color: #ffffff;
        }

        /* ========== Sidebar Brand เรียบง่าย ========== */
        .sidebar .sidebar-brand {
            color: #ffffff;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar .sidebar-brand .brand-icon {
            width: 40px;
            height: 40px;
            background: #6c757d;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
        }

        .sidebar .sidebar-brand .brand-icon i {
            color: white;
            font-size: 1.25rem;
        }

        .sidebar .sidebar-brand h5 {
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
            font-weight: 600;
            color: #ffffff;
        }

        .sidebar .sidebar-brand small {
            font-size: 0.7rem;
            color: rgba(255, 255, 255, 0.6);
        }



        .sidebar hr {
            border: none;
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 1rem;
        }

        /* ========== Simple sidebar icons ========== */
        .sidebar .nav-link i {
            width: 18px;
            text-align: center;
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.7);
        }



        .sidebar .nav-link span {
            font-size: 0.875rem;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.8);
        }

        .sidebar .nav-link:hover span,
        .sidebar .nav-link.active span {
            color: #ffffff;
        }

        /* ========== Main Content Wrapper ========== */
        .main-content-wrapper {
            margin-left: 250px;
            width: calc(100% - 250px);
            min-height: 100vh;
        }

        /* ========== Main Content Area - เรียบง่าย ========== */
        .main-content {
            min-height: 100vh;
            background: #ffffff;
            padding: 0;
        }

        .content-wrapper {
            padding: 1.5rem;
            min-height: 100vh;
            background: transparent;
        }

        .navbar-admin {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 107, 157, 0.2);
            border-radius: 0 0 var(--admin-border-radius-lg) var(--admin-border-radius-lg);
            box-shadow: var(--admin-shadow-soft);
            margin-bottom: 25px;
            position: relative;
            z-index: 1020;
            padding: 20px 30px;
            transition: all 0.3s ease;
        }

        /* ========== Cards - การ์ดที่สวยงามสำหรับเด็ก ========== */
        .card {
            border: none;
            box-shadow: var(--admin-shadow-soft);
            border-radius: var(--admin-border-radius-lg);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 107, 157, 0.1);
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
                var(--child-pink) 0%,
                var(--child-blue) 25%,
                var(--child-green) 50%,
                var(--child-yellow) 75%,
                var(--child-orange) 100%);
            z-index: 1;
        }

        .card:hover {
            box-shadow: var(--admin-shadow-strong);
            transform: translateY(-8px) scale(1.02);
        }

        .card-header {
            background: linear-gradient(135deg,
                var(--admin-primary) 0%,
                var(--admin-secondary) 100%);
            border-bottom: none;
            font-weight: 600;
            color: white;
            padding: 1.5rem;
            position: relative;
            z-index: 2;
        }

        /* ========== Buttons - ปุ่มที่น่ารักและใช้งานง่าย ========== */
        .btn-primary {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            border: none;
            border-radius: var(--admin-border-radius);
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--admin-shadow-soft);
            color: var(--admin-text-light);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1B4F72, #2C3E50);
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-medium);
            color: var(--admin-white);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--admin-blue), var(--admin-accent));
            border: none;
            border-radius: var(--admin-border-radius);
            box-shadow: var(--admin-shadow-soft);
            color: var(--admin-white);
        }

        .btn-info {
            background: linear-gradient(135deg, var(--admin-accent), var(--admin-purple));
            border: none;
            border-radius: var(--admin-border-radius);
            color: var(--admin-white);
            box-shadow: var(--admin-shadow-soft);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--admin-orange), var(--admin-purple));
            border: none;
            border-radius: var(--admin-border-radius);
            color: var(--admin-text-dark);
            box-shadow: var(--admin-shadow-soft);
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        /* ========== Tables - ตารางที่สวยงาม ========== */
        .table {
            border-radius: var(--admin-border-radius);
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(52, 152, 219, 0.08);
        }

        .table th {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            border-top: none;
            font-weight: 600;
            color: white;
            padding: 15px;
            border-bottom: none;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .table td {
            padding: 15px;
            vertical-align: middle;
            border-color: rgba(52, 152, 219, 0.1);
            color: #2C3E50;
            font-weight: 500;
        }

        .table td strong {
            color: #1B4F72;
            font-weight: 600;
        }

        .table td small {
            color: #34495E;
            font-weight: 500;
        }

        .table-hover tbody tr:hover {
            background: linear-gradient(135deg,
                rgba(52, 152, 219, 0.08) 0%,
                rgba(41, 128, 185, 0.05) 100%);
            transform: translateY(-1px);
            transition: all 0.3s ease;
        }

        .table-hover tbody tr:hover td {
            color: #1B4F72;
        }

        .table-hover tbody tr:hover td small {
            color: #2C3E50;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        /* ========== Statistics Cards - การ์ดสถิติที่น่าสนใจ ========== */
        .stats-card {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            color: var(--admin-white);
            border-radius: var(--admin-border-radius-lg);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: none;
            box-shadow: 0 8px 30px rgba(52, 152, 219, 0.15);
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
            transform: rotate(45deg);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 50px rgba(52, 152, 219, 0.25);
        }

        .stats-card:hover::before {
            background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
        }

        .stats-card .stats-icon {
            font-size: 3.5rem;
            opacity: 0.9;
            transition: all 0.3s ease;
        }

        .stats-card:hover .stats-icon {
            transform: scale(1.1) rotate(5deg);
            opacity: 1;
        }

        /* Different colored stats cards */
        .news-card {
            background: linear-gradient(135deg, #3498DB, #5DADE2);
        }

        .staff-card {
            background: linear-gradient(135deg, #2ECC71, #58D68D);
        }

        .images-card {
            background: linear-gradient(135deg, #E74C3C, #EC7063);
        }



        .stats-card:hover .stats-icon {
            transform: scale(1.1) rotate(5deg);
        }

        /* ========== สีสันสำหรับการ์ดสถิติแต่ละประเภท ========== */
        .stats-card.news-card {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
        }

        .stats-card.staff-card {
            background: linear-gradient(135deg, var(--admin-secondary), var(--admin-dark));
        }

        .stats-card.images-card {
            background: linear-gradient(135deg, var(--admin-accent), var(--admin-light));
        }



        /* ========== Mobile Sidebar Toggle Button ========== */
        #sidebarToggle {
            border: 2px solid var(--admin-primary);
            color: var(--admin-primary);
            background: transparent;
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.3s ease;
        }

        #sidebarToggle:hover {
            background: var(--admin-primary);
            color: white;
            transform: scale(1.05);
        }

        /* ========== Responsive Design ========== */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 250px;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content-wrapper {
                margin-left: 0;
                width: 100%;
            }

            .main-content {
                margin-left: 0;
            }

            .stats-card .stats-icon {
                font-size: 2rem;
            }

            /* เพิ่ม overlay เมื่อเปิด sidebar ในมือถือ */
            .sidebar.show::after {
                content: '';
                position: fixed;
                top: 0;
                left: 250px;
                width: calc(100vw - 250px);
                height: 100vh;
                background: rgba(0, 0, 0, 0.5);
                z-index: -1;
            }
        }

        /* ========== Desktop Design (หน้าจอใหญ่) ========== */
        @media (min-width: 769px) {
            .sidebar {
                position: fixed;
                transform: translateX(0);
            }

            .main-content-wrapper {
                margin-left: 250px;
                width: calc(100% - 250px);
            }
        }

        /* ========== Animation Effects ========== */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        .stats-card:nth-child(1) { animation-delay: 0.1s; }
        .stats-card:nth-child(2) { animation-delay: 0.2s; }
        .stats-card:nth-child(3) { animation-delay: 0.3s; }
        .stats-card:nth-child(4) { animation-delay: 0.4s; }

        /* ========== Welcome Section ========== */
        .welcome-section {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-accent));
            border-radius: var(--admin-border-radius-lg);
            position: relative;
            overflow: hidden;
            box-shadow: var(--admin-shadow-strong);
            color: var(--admin-text-light);
            padding: 2.5rem;
            margin-bottom: 2rem;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(236, 240, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(236, 240, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(52, 152, 219, 0.08) 0%, transparent 70%);
            pointer-events: none;
        }

        /* ========== Badge และ Status ========== */
        .badge {
            border-radius: var(--admin-border-radius);
            padding: 8px 12px;
            font-weight: 500;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, var(--admin-secondary), var(--admin-purple)) !important;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, var(--admin-accent), var(--admin-orange)) !important;
            color: var(--admin-text-dark) !important;
        }

        .badge.bg-danger {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary)) !important;
        }

        /* ========== Dropdown Menu Fixes ========== */
        .dropdown-menu {
            z-index: 1030 !important;
            box-shadow: var(--admin-shadow-medium);
            border: 1px solid rgba(44, 62, 80, 0.1);
            border-radius: var(--admin-border-radius);
            background: var(--admin-white);
            backdrop-filter: blur(10px);
            margin-top: 8px;
        }

        .dropdown-item {
            color: var(--admin-text-dark);
            padding: 10px 16px;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 2px 4px;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg,
                rgba(44, 62, 80, 0.05) 0%,
                rgba(52, 152, 219, 0.08) 100%);
            color: var(--admin-text-dark);
            transform: translateX(4px);
        }

        .dropdown-item:focus {
            background: linear-gradient(135deg,
                rgba(44, 62, 80, 0.08) 0%,
                rgba(52, 152, 219, 0.1) 100%);
            color: var(--admin-text-dark);
        }

        .dropdown-divider {
            border-color: rgba(44, 62, 80, 0.1);
            margin: 8px 0;
        }

        /* ========== Navbar Enhancements ========== */
        .navbar-admin::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(255, 182, 193, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(135, 206, 235, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 80%, rgba(152, 251, 152, 0.08) 0%, transparent 50%),
                linear-gradient(90deg,
                    rgba(255, 230, 109, 0.05) 0%,
                    rgba(221, 160, 221, 0.05) 100%);
            pointer-events: none;
        }

        .navbar-brand {
            font-weight: 700;
            background: linear-gradient(45deg,
                #FF6B9D 0%,
                #4ECDC4 25%,
                #45B7D1 50%,
                #96CEB4 75%,
                #FFEAA7 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.4rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .navbar-brand::after {
            content: '🌈';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(-50%); }
            40% { transform: translateY(-60%); }
            60% { transform: translateY(-55%); }
        }

        .navbar-nav .nav-link {
            color: #2C3E50 !important;
            font-weight: 600;
            padding: 10px 18px !important;
            border-radius: var(--admin-border-radius-lg);
            margin: 0 6px;
            transition: all 0.4s ease;
            position: relative;
            border: 2px solid transparent;
        }

        .navbar-nav .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 182, 193, 0.2) 0%,
                rgba(135, 206, 235, 0.2) 50%,
                rgba(152, 251, 152, 0.2) 100%);
            border-radius: var(--admin-border-radius-lg);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .navbar-nav .nav-link:hover::before {
            opacity: 1;
        }

        .navbar-nav .nav-link:hover {
            color: #FF6B9D !important;
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
            border-color: rgba(255, 107, 157, 0.3);
        }

        .navbar-nav .nav-link.active {
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.15) 0%,
                rgba(78, 205, 196, 0.15) 50%,
                rgba(255, 230, 109, 0.15) 100%);
            color: #FF6B9D !important;
            font-weight: 700;
            border-color: rgba(255, 107, 157, 0.4);
            box-shadow: 0 3px 12px rgba(255, 107, 157, 0.2);
        }

        /* ========== User Profile Dropdown ========== */
        .navbar .dropdown-toggle::after {
            display: none;
        }

        .navbar .dropdown-toggle {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
            border: 2px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 20px !important;
            padding: 12px 24px !important;
            color: #ffffff !important;
            transition: all 0.3s ease !important;
            font-weight: 600 !important;
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3) !important;
            position: relative !important;
            overflow: hidden !important;
            text-decoration: none !important;
        }

        .navbar .dropdown-toggle:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(30, 64, 175, 0.4) !important;
            border-color: rgba(255, 255, 255, 0.3) !important;
            color: #ffffff !important;
        }

        .navbar .dropdown-toggle::before {
            content: '👤';
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .navbar .dropdown-toggle::after {
            content: '▼';
            margin-left: 10px;
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }

        /* ========== Dropdown Menu Styles ========== */
        .navbar .dropdown-menu {
            background: #ffffff !important;
            border: 2px solid rgba(30, 64, 175, 0.1) !important;
            border-radius: 15px !important;
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.15) !important;
            padding: 10px 0 !important;
            margin-top: 10px !important;
        }

        .navbar .dropdown-item {
            color: #1e40af !important;
            padding: 12px 20px !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
            border-radius: 10px !important;
            margin: 2px 8px !important;
        }

        .navbar .dropdown-item:hover {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
            color: #ffffff !important;
            transform: translateX(5px) !important;
        }

        .navbar .dropdown-item i {
            color: inherit !important;
            width: 20px !important;
            text-align: center !important;
        }

        .navbar .dropdown-toggle:hover {
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.2) 0%,
                rgba(255, 255, 255, 0.95) 50%,
                rgba(78, 205, 196, 0.2) 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.3);
        }

        .navbar .dropdown-toggle:hover::after {
            transform: rotate(180deg);
        }

        .navbar .dropdown-toggle:focus {
            box-shadow: 0 0 0 4px rgba(255, 107, 157, 0.3);
            outline: none;
        }
    </style>
    
    <?php echo $__env->yieldContent('styles'); ?>
</head>
<body class="childcare-theme">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-4">
                        <div class="sidebar-brand">
                            <div class="brand-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <h5 class="mb-0 fw-bold">
                                ⚙️ ระบบจัดการ
                            </h5>
                            <small class="text-white-50">ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า</small>
                        </div>
                        
                        <nav class="nav flex-column">
                            <a class="nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>"
                               href="<?php echo e(route('admin.dashboard')); ?>">
                                <i class="fas fa-home"></i>
                                <span>หน้าหลัก</span>
                            </a>

                            <a class="nav-link <?php echo e(request()->routeIs('admin.news.*') ? 'active' : ''); ?>"
                               href="<?php echo e(route('admin.news.index')); ?>">
                                <i class="fas fa-newspaper"></i>
                                <span>จัดการข่าวสาร</span>
                            </a>

                            <a class="nav-link <?php echo e(request()->routeIs('admin.staff.*') ? 'active' : ''); ?>"
                               href="<?php echo e(route('admin.staff.index')); ?>">
                                <i class="fas fa-users"></i>
                                <span>จัดการบุคลากร</span>
                            </a>





                            <a class="nav-link <?php echo e(request()->routeIs('admin.images.*') ? 'active' : ''); ?>"
                               href="<?php echo e(route('admin.images.index')); ?>">
                                <i class="fas fa-images"></i>
                                <span>จัดการรูปภาพ</span>
                            </a>



                            <hr class="my-3">

                            <a class="nav-link" href="<?php echo e(route('home')); ?>" target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                                <span>ดูเว็บไซต์</span>
                            </a>

                            <a class="nav-link" href="<?php echo e(route('logout')); ?>"
                               onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>ออกจากระบบ</span>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
            
            <!-- Main Content (ปรับสำหรับ Fixed Sidebar) -->
            <div class="main-content-wrapper">
                <div class="main-content">
                    <!-- Top Navbar -->
                    <nav class="navbar navbar-expand-lg navbar-admin">
                        <div class="container-fluid">
                            <!-- Mobile Menu Toggle -->
                            <button class="btn btn-outline-primary d-md-none me-3" type="button" id="sidebarToggle">
                                <i class="fas fa-bars"></i>
                            </button>

                            <h4 class="mb-0 text-primary"><?php echo $__env->yieldContent('page-title', 'ระบบจัดการ'); ?></h4>
                            
                            <div class="navbar-nav ms-auto">
                                <div class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-user-circle me-2"></i>
                                        <?php echo e(Auth::user()->name); ?>

                                    </a>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('home')); ?>" target="_blank">
                                                <i class="fas fa-external-link-alt me-2"></i>ดูเว็บไซต์
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('logout')); ?>"
                                               onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </nav>
                    
                    <!-- Page Content -->
                    <div class="content-wrapper">
                        <?php if(session('success')): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo e(session('success')); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if(session('error')): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo e(session('error')); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php echo $__env->yieldContent('content'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Logout Form -->
    <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
        <?php echo csrf_field(); ?>
    </form>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Admin Childcare JS -->
    <script src="<?php echo e(asset('js/admin-childcare.js')); ?>"></script>
    <!-- Image Optimization JS -->
    <script src="<?php echo e(asset('js/image-optimization.js')); ?>"></script>
    <!-- Force Normal Colors JS -->
    <script src="<?php echo e(asset('js/force-normal-colors.js')); ?>"></script>

    <!-- Fixed Sidebar JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.querySelector('.sidebar');

            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });

                // ปิด sidebar เมื่อคลิกนอกพื้นที่ (สำหรับมือถือ)
                document.addEventListener('click', function(e) {
                    if (window.innerWidth <= 768) {
                        if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                            sidebar.classList.remove('show');
                        }
                    }
                });

                // ปรับขนาดหน้าจอ
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        sidebar.classList.remove('show');
                    }
                });
            }
        });
    </script>

    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\childcenter\resources\views/layouts/admin.blade.php ENDPATH**/ ?>