<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ActivityImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'description',
        'image_path',
        'thumbnail_path',
        'is_published'
    ];

    protected $casts = [
        'is_published' => 'boolean'
    ];





    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    // Accessor สำหรับ URL รูปภาพ
    public function getImageUrlAttribute()
    {
        if (!$this->image_path) {
            return null;
        }
        // แปลง backslash เป็น forward slash สำหรับ Windows
        $normalizedPath = str_replace('\\', '/', $this->image_path);
        return asset('storage/' . $normalizedPath);
    }

    // Accessor สำหรับ URL thumbnail
    public function getThumbnailUrlAttribute()
    {
        if (!$this->thumbnail_path) {
            return $this->image_url;
        }
        // แปลง backslash เป็น forward slash สำหรับ Windows
        $normalizedThumbnailPath = str_replace('\\', '/', $this->thumbnail_path);
        return asset('storage/' . $normalizedThumbnailPath);
    }
}
