@extends('layouts.admin')

@section('title', 'หน้าหลักแดชบอร์ด - ระบบจัดการ')
@section('page-title', 'หน้าหลักแดชบอร์ด')

@section('styles')
<style>
    /* ========== Dashboard Layout Improvements ========== */
    .dashboard-container {
        width: 100%;
        margin: 0;
        padding: 1.5rem 2rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: calc(100vh - 60px);
    }

    /* Full width container */
    .container-fluid {
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* ========== Enhanced Stats Cards ========== */
    .stats-card {
        background: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        height: 100%;
        min-height: 160px;
        overflow: hidden;
    }

    .stats-card .card-body {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        height: 100%;
    }

    .stats-card h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
        color: #495057;
    }

    .stats-card .card-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6c757d;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stats-card small {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
    }

    .stats-icon i {
        font-size: 1.5rem;
        color: #ffffff;
    }

    /* ========== Color Themes for Stats Cards ========== */
    .news-card {
        border-left: 4px solid #17a2b8;
    }

    .news-card .stats-icon {
        background: linear-gradient(135deg, #17a2b8, #138496);
    }



    .images-card {
        border-left: 4px solid #6c757d;
    }

    .images-card .stats-icon {
        background: linear-gradient(135deg, #6c757d, #495057);
    }

    .staff-card {
        border-left: 4px solid #28a745;
    }

    .staff-card .stats-icon {
        background: linear-gradient(135deg, #28a745, #1e7e34);
    }



    /* ========== Card Link Hover Effects ========== */
    .stats-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    a .stats-card h2,
    a .stats-card .card-title,
    a .stats-card small {
        color: inherit;
    }

    a:hover .stats-card h2,
    a:hover .stats-card .card-title,
    a:hover .stats-card small {
        color: inherit;
    }

    /* ========== Welcome Section ========== */
    .welcome-section {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 16px;
        padding: 2.5rem 3rem;
        border: 1px solid #e9ecef;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        width: 100%;
    }

    .welcome-section h1 {
        color: #495057;
        font-weight: 600;
    }

    /* ========== Dashboard Stats Section ========== */
    .dashboard-stats {
        margin-bottom: 3rem;
    }

    .dashboard-stats .row {
        margin-left: -0.75rem;
        margin-right: -0.75rem;
    }

    .dashboard-stats .col-xl-3,
    .dashboard-stats .col-lg-6,
    .dashboard-stats .col-md-6 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* ========== Enhanced Cards ========== */
    .card {
        border: 1px solid #e9ecef;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
    }

    .card-header {
        background: #ffffff;
        border-bottom: 1px solid #e9ecef;
        padding: 1.25rem;
        border-radius: 12px 12px 0 0;
    }

    .card-body {
        padding: 1.5rem;
    }

    /* ========== Table Improvements ========== */
    .table {
        margin-bottom: 0;
    }

    .table th {
        background: #f8f9fa;
        border-bottom: 2px solid #e9ecef;
        font-weight: 600;
        color: #495057;
        font-size: 0.875rem;
        padding: 1rem 0.75rem;
    }

    .table td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
    }



    /* ========== Badge Improvements ========== */
    .badge {
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
    }

    .badge.bg-success {
        background-color: #28a745 !important;
    }

    .badge.bg-warning {
        background-color: #ffc107 !important;
        color: #212529 !important;
    }

    /* ========== Button Improvements ========== */
    .btn {
        border-radius: 8px;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border: 1px solid transparent;
    }

    .btn-primary {
        background-color: #6c757d;
        border-color: #6c757d;
    }



    .btn-outline-primary {
        color: #6c757d;
        border-color: #6c757d;
    }



    .btn-outline-secondary {
        color: #6c757d;
        border-color: #6c757d;
    }



    /* ========== Timeline Styles ========== */
    .timeline {
        position: relative;
    }

    .timeline-item {
        position: relative;
    }

    .timeline-marker {
        flex-shrink: 0;
        box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.1);
    }

    .timeline-content {
        padding-left: 0.5rem;
    }

    .timeline-content p {
        color: #495057;
        font-weight: 500;
    }

    /* ========== Balanced Layout ========== */
    .image-container {
        /* No hover effects */
    }

    /* ========== Responsive Improvements ========== */
    @media (max-width: 1200px) {
        .dashboard-container {
            padding: 1.5rem 1rem;
        }

        .welcome-section {
            padding: 2rem;
        }
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 1rem 0.5rem;
        }

        .welcome-section {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .stats-card h2 {
            font-size: 2rem;
        }

        .stats-card .card-body {
            padding: 1.25rem;
        }

        .stats-icon {
            width: 50px;
            height: 50px;
        }

        .stats-icon i {
            font-size: 1.25rem;
        }

        /* Mobile: Stack columns */
        .col-lg-7,
        .col-lg-5 {
            margin-bottom: 1rem;
        }

        .dashboard-stats .row {
            margin-left: -0.5rem;
            margin-right: -0.5rem;
        }

        .dashboard-stats .col-xl-4,
        .dashboard-stats .col-lg-6,
        .dashboard-stats .col-md-6 {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }
    }

    /* Old styles removed for cleaner design */
</style>
@endsection

@section('content')
<div class="dashboard-container">
    <div class="container-fluid">
        <!-- Welcome Section -->
        <div class="welcome-section mb-4">
            <div class="row align-items-center">
                <div class="col-12">
                    <h1 class="h3 mb-2 text-dark">ยินดีต้อนรับสู่ระบบจัดการ</h1>
                    <p class="text-muted mb-0">ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า</p>
                </div>
            </div>
        </div>

    <!-- Dashboard Statistics Section -->
    <div class="dashboard-stats">
        <div class="row g-4 mb-5">
            <!-- Statistics Cards -->
            <div class="col-xl-3 col-lg-6 col-md-6">
                <a href="{{ route('admin.news.index') }}" class="text-decoration-none">
                    <div class="card stats-card news-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center w-100">
                                <div class="flex-grow-1">
                                    <h6 class="card-title">ข่าวสาร</h6>
                                    <h2>{{ $stats['news_count'] }}</h2>
                                    <small>รายการข่าวสาร</small>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-newspaper"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-lg-6 col-md-6">
                <a href="{{ route('admin.images.index') }}" class="text-decoration-none">
                    <div class="card stats-card images-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center w-100">
                                <div class="flex-grow-1">
                                    <h6 class="card-title">รูปภาพ</h6>
                                    <h2>{{ $stats['images_count'] }}</h2>
                                    <small>รูปกิจกรรม</small>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-images"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>



            <div class="col-xl-3 col-lg-6 col-md-6">
                <a href="{{ route('admin.staff.index') }}" class="text-decoration-none">
                    <div class="card stats-card staff-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center w-100">
                                <div class="flex-grow-1">
                                    <h6 class="card-title">บุคลากร</h6>
                                    <h2>{{ $stats['staff_count'] }}</h2>
                                    <small>จำนวนบุคลากร</small>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Recent News -->
        <div class="col-lg-7">
            <div class="card h-100">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-dark">
                            <i class="fas fa-newspaper me-2 text-info"></i>
                            ข่าวสารล่าสุด
                        </h5>
                        <a href="{{ route('admin.news.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>ดูทั้งหมด
                        </a>
                    </div>
                </div>
            <div class="card-body">
                @if($recent_news->count() > 0)
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>หัวข้อ</th>
                                    <th>วันที่เผยแพร่</th>
                                    <th>สถานะ</th>
                                    <th>การจัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recent_news as $news)
                                <tr>
                                    <td>
                                        <strong>{{ Str::limit($news->title, 50) }}</strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $news->created_at->format('d/m/Y') }}
                                        </small>
                                    </td>
                                    <td>
                                        @if($news->is_published)
                                            <span class="badge bg-success">เผยแพร่</span>
                                        @else
                                            <span class="badge bg-warning">ร่าง</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.news.edit', $news->id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <p class="text-muted">ยังไม่มีข่าวสาร</p>
                        <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>เพิ่มข่าวสาร
                        </a>
                    </div>
                @endif


            </div>
        </div>
    </div>
    
        <!-- Right Sidebar -->
        <div class="col-lg-5">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-bolt me-2 text-warning"></i>
                        การจัดการด่วน
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <a href="{{ route('admin.news.create') }}" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>เพิ่มข่าวสาร
                            </a>
                        </div>

                        <div class="col-6">
                            <a href="{{ route('admin.staff.index') }}" class="btn btn-success w-100">
                                <i class="fas fa-users me-2"></i>จัดการบุคลากร
                            </a>
                        </div>



                        <div class="col-6">
                            <a href="{{ route('admin.images.create') }}" class="btn btn-info w-100">
                                <i class="fas fa-image me-2"></i>อัพโหลดรูปภาพ
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Images -->
            <div class="card mb-4">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-images me-2 text-secondary"></i>
                        รูปภาพล่าสุด
                    </h5>
                </div>
                <div class="card-body">
                    @if($recent_images->count() > 0)
                        <div class="row g-2">
                            @foreach($recent_images->take(6) as $image)
                            <div class="col-4">
                                <div class="image-container" style="width: 100%; aspect-ratio: 1; overflow: hidden; border-radius: 8px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center; border: 1px solid #e9ecef;">
                                    <img src="{{ $image->thumbnail_url ?? $image->image_url }}"
                                         alt="{{ $image->description ?? 'รูปภาพกิจกรรม' }}"
                                         class="img-fluid"
                                         style="max-width: 100%; max-height: 100%; object-fit: cover; border-radius: 8px;">
                                </div>
                            </div>
                            @endforeach
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ route('admin.images.index') }}" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-eye me-1"></i>ดูทั้งหมด
                            </a>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-images fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-2">ยังไม่มีรูปภาพ</p>
                            <a href="{{ route('admin.images.create') }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus me-1"></i>เพิ่มรูปภาพ
                            </a>
                        </div>
                    @endif
                </div>
            </div>


        </div>
    </div>
    </div> <!-- Close container-fluid -->
</div>
@endsection
