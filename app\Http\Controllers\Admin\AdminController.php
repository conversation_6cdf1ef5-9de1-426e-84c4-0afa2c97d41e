<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\ActivityImage;
use App\Models\Staff;

use Illuminate\Http\Request;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function dashboard()
    {
        $stats = [
            'news_count' => News::count(),
            'images_count' => ActivityImage::count(),
            'staff_count' => Staff::count(),
        ];

        $recent_news = News::latest()->take(5)->get();
        $recent_images = ActivityImage::latest()->take(6)->get();

        return view('admin.dashboard', compact('stats', 'recent_news', 'recent_images'));
    }
}
