<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\StaffController;
use App\Http\Controllers\GalleryController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\NewsController as AdminNewsController;
use App\Http\Controllers\Admin\StaffController as AdminStaffController;
use App\Http\Controllers\Admin\ActivityImageController;




/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Frontend Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/news', [NewsController::class, 'index'])->name('news.index');
Route::get('/news/{id}', [NewsController::class, 'show'])->name('news.show');
Route::get('/staff', [StaffController::class, 'index'])->name('staff.index');
Route::get('/gallery', [GalleryController::class, 'index'])->name('gallery.index');


Route::get('/contact', function () {
    return view('contact');
})->name('contact');



// Authentication Routes
Auth::routes(['register' => false]);

// Admin Routes
Route::prefix('admin')->name('admin.')->middleware('auth')->group(function () {
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');

    // News Management
    Route::resource('news', AdminNewsController::class);
    Route::patch('news/{news}/toggle-status', [AdminNewsController::class, 'toggleStatus'])->name('news.toggle-status');
    Route::get('news/{news}/images', [AdminNewsController::class, 'manageImages'])->name('news.images.manage');
    Route::post('news/images/{image}/featured', [AdminNewsController::class, 'setFeatured'])->name('news.images.featured');
    Route::delete('news/images/{image}', [AdminNewsController::class, 'deleteImage'])->name('news.images.delete');

    // Staff Management
    Route::resource('staff', AdminStaffController::class);
    Route::patch('staff/{staff}/toggle-status', [AdminStaffController::class, 'toggleStatus'])->name('staff.toggle-status');





    // Activity Images Management
    Route::resource('images', ActivityImageController::class);
    Route::patch('images/{image}/toggle-status', [ActivityImageController::class, 'toggleStatus'])->name('images.toggle-status');


});

Auth::routes();

Route::get('/home', function () {
    return redirect('/admin');
})->middleware('auth');
