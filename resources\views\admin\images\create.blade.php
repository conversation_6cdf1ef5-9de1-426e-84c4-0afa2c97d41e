@extends('layouts.admin')

@section('title', 'อัพโหลดรูปภาพ - ระบบจัดการ')
@section('page-title', 'อัพโหลดรูปภาพกิจกรรม')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">


        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i>อัพโหลดรูปภาพใหม่
                </h5>
                <small class="text-muted">สามารถอัพโหลดได้ไม่จำกัดจำนวน</small>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.images.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    

                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย</label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description"
                                  name="description"
                                  rows="3">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>



                    <!-- Upload Mode Selection -->
                    <div class="mb-3">
                        <label class="form-label">โหมดการอัพโหลด</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="upload_mode" id="single_mode" value="single" checked>
                            <label class="btn btn-outline-primary" for="single_mode">
                                <i class="fas fa-image me-2"></i>อัพโหลดรูปเดียว
                            </label>

                            <input type="radio" class="btn-check" name="upload_mode" id="multiple_mode" value="multiple">
                            <label class="btn btn-outline-success" for="multiple_mode">
                                <i class="fas fa-images me-2"></i>อัพโหลดหลายรูป
                            </label>
                        </div>
                    </div>

                    <!-- Single Image Upload -->
                    <div id="single_upload" class="upload-section">
                        <div class="mb-3">
                            <label for="image" class="form-label">รูปภาพ <span class="text-danger">*</span></label>
                            <input type="file"
                                   class="form-control @error('image') is-invalid @enderror"
                                   id="image"
                                   name="image"
                                   accept="image/*"
                                   onchange="previewSingleImage(this)">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 5MB)</div>
                        </div>

                        <!-- Single Image Preview -->
                        <div class="mb-3" id="singleImagePreview" style="display: none;">
                            <label class="form-label">ตัวอย่างรูปภาพ</label>
                            <div>
                                <img id="singlePreview" src="" alt="Preview" class="img-thumbnail" style="max-width: 400px; max-height: 300px;">
                            </div>
                        </div>
                    </div>

                    <!-- Multiple Images Upload -->
                    <div id="multiple_upload" class="upload-section" style="display: none;">
                        <div class="mb-3">
                            <label for="images" class="form-label">รูปภาพหลายรูป <span class="text-danger">*</span></label>
                            <input type="file"
                                   class="form-control @error('images') is-invalid @enderror @error('images.*') is-invalid @enderror"
                                   id="images"
                                   name="images[]"
                                   accept="image/*"
                                   multiple
                                   onchange="previewMultipleImages(this)">
                            @error('images')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            @error('images.*')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 5MB ต่อรูป) | สามารถเลือกได้ไม่จำกัดจำนวน
                            </div>

                            <!-- แสดงจำนวนรูปที่เลือก -->
                            <div id="fileCountDisplay" class="mt-2" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <div class="progress flex-grow-1 me-3" style="height: 20px;">
                                        <div id="fileCountProgress" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <span id="fileCountText" class="badge bg-primary">0 รูป</span>
                                </div>
                            </div>
                        </div>

                        <!-- Multiple Images Preview -->
                        <div class="mb-3" id="multipleImagesPreview" style="display: none;">
                            <label class="form-label">ตัวอย่างรูปภาพ (<span id="imageCount">0</span> รูป)</label>
                            <div id="previewContainer" class="row g-2"></div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAllPreviews()">
                                    <i class="fas fa-trash me-1"></i>ลบทั้งหมด
                                </button>
                            </div>
                        </div>

                        <!-- Info for Multiple Upload -->
                        <div class="alert alert-info">
                            <h6 class="mb-2">
                                <i class="fas fa-info-circle me-2"></i>การตั้งค่าสำหรับการอัปโหลดหลายรูป
                            </h6>
                            <p class="mb-0">
                                ระบบจะใช้ชื่อ คำอธิบาย และวันที่เดียวกันสำหรับทุกรูป
                                โดยชื่อรูปจะเพิ่มหมายเลขต่อท้ายอัตโนมัติ (เช่น "ชื่อรูป - รูปที่ 1", "ชื่อรูป - รูปที่ 2")
                            </p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input"
                                   type="checkbox"
                                   id="is_published"
                                   name="is_published"
                                   value="1"
                                   {{ old('is_published', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_published">
                                เผยแพร่ทันที
                            </label>
                        </div>
                        <div class="form-text">หากไม่เลือก รูปภาพจะถูกบันทึกเป็นร่าง</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.images.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>กลับ
                        </a>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i>อัพโหลด
                            </button>
                        </div>
                    </div>

                    <!-- คำแนะนำการใช้งาน -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-lightbulb me-2 text-warning"></i>คำแนะนำการอัพโหลดแบบแบ่งหน้า
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0 small">
                                    <li>สามารถอัพโหลดได้ <strong>ไม่จำกัดจำนวน</strong> ต่อครั้ง</li>
                                    <li>หลังจากอัพโหลดเสร็จ จะมีปุ่ม <strong>"อัพโหลดต่อ"</strong> สำหรับชุดถัดไป</li>
                                    <li>แนะนำให้ตั้งชื่อรูปภาพให้สื่อความหมาย เช่น "กิจกรรมวันเด็ก", "การเรียนการสอน"</li>
                                    <li>รูปภาพจะถูกย่อขนาดอัตโนมัติเพื่อประหยัดพื้นที่</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <div class="text-center">
                                    <div class="d-inline-block p-3 bg-white rounded shadow-sm">
                                        <i class="fas fa-images fa-3x text-primary mb-2"></i>
                                        <div class="small">
                                            <strong>ขั้นตอนการอัพโหลด</strong><br>
                                            1. เลือกรูปภาพ (ไม่จำกัดจำนวน)<br>
                                            2. ตั้งชื่อและคำอธิบาย<br>
                                            3. กดอัพโหลด<br>
                                            4. กด "อัพโหลดต่อ" สำหรับชุดถัดไป
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Upload mode toggle
    const singleMode = document.getElementById('single_mode');
    const multipleMode = document.getElementById('multiple_mode');
    const singleUpload = document.getElementById('single_upload');
    const multipleUpload = document.getElementById('multiple_upload');
    const singleImageInput = document.getElementById('image');
    const multipleImagesInput = document.getElementById('images');

    // Toggle upload modes
    singleMode.addEventListener('change', function() {
        if (this.checked) {
            singleUpload.style.display = 'block';
            multipleUpload.style.display = 'none';
            singleImageInput.required = true;
            multipleImagesInput.required = false;
            clearAllPreviews();
        }
    });

    multipleMode.addEventListener('change', function() {
        if (this.checked) {
            singleUpload.style.display = 'none';
            multipleUpload.style.display = 'block';
            singleImageInput.required = false;
            multipleImagesInput.required = true;
            clearSinglePreview();
        }
    });
});

// Single image preview
function previewSingleImage(input) {
    const preview = document.getElementById('singlePreview');
    const previewContainer = document.getElementById('singleImagePreview');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            previewContainer.style.display = 'block';
        }

        reader.readAsDataURL(input.files[0]);
    } else {
        previewContainer.style.display = 'none';
    }
}

// Multiple images preview
function previewMultipleImages(input) {
    const previewContainer = document.getElementById('previewContainer');
    const multipleImagesPreview = document.getElementById('multipleImagesPreview');
    const imageCount = document.getElementById('imageCount');
    const fileCountDisplay = document.getElementById('fileCountDisplay');
    const fileCountProgress = document.getElementById('fileCountProgress');
    const fileCountText = document.getElementById('fileCountText');

    // Clear previous previews
    previewContainer.innerHTML = '';

    if (input.files && input.files.length > 0) {
        const fileCount = input.files.length;

        imageCount.textContent = fileCount;
        fileCountText.textContent = `${fileCount} รูป`;
        fileCountProgress.style.width = '100%';

        // เปลี่ยนสีตามจำนวน
        if (fileCount <= 50) {
            fileCountProgress.className = 'progress-bar bg-success';
        } else if (fileCount <= 100) {
            fileCountProgress.className = 'progress-bar bg-warning';
        } else {
            fileCountProgress.className = 'progress-bar bg-danger';
        }

        multipleImagesPreview.style.display = 'block';
        fileCountDisplay.style.display = 'block';

        Array.from(input.files).forEach((file, index) => {
            // Check file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert(`รูปที่ ${index + 1} มีขนาดเกิน 5MB`);
                return;
            }

            const reader = new FileReader();

            reader.onload = function(e) {
                const col = document.createElement('div');
                col.className = 'col-md-3 col-sm-4 col-6';

                col.innerHTML = `
                    <div class="card">
                        <img src="${e.target.result}" class="card-img-top" style="height: 150px; object-fit: cover;">
                        <div class="card-body p-2">
                            <small class="text-muted">รูปที่ ${index + 1}</small>
                            <br>
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                            <button type="button" class="btn btn-sm btn-outline-danger mt-1 w-100"
                                    onclick="removePreview(this, ${index})">
                                <i class="fas fa-trash"></i> ลบ
                            </button>
                        </div>
                    </div>
                `;

                previewContainer.appendChild(col);
            }

            reader.readAsDataURL(file);
        });
    } else {
        multipleImagesPreview.style.display = 'none';
    }
}

// Remove single preview
function removePreview(button, index) {
    const input = document.getElementById('images');

    // สร้าง DataTransfer object ใหม่
    const dt = new DataTransfer();

    // เพิ่มไฟล์ทั้งหมดยกเว้นไฟล์ที่ต้องการลบ
    Array.from(input.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    // อัปเดต input files
    input.files = dt.files;

    // รีเฟรช preview
    previewMultipleImages(input);
}

// Clear all previews
function clearAllPreviews() {
    const previewContainer = document.getElementById('previewContainer');
    const multipleImagesPreview = document.getElementById('multipleImagesPreview');
    const multipleImagesInput = document.getElementById('images');
    const fileCountDisplay = document.getElementById('fileCountDisplay');

    previewContainer.innerHTML = '';
    multipleImagesPreview.style.display = 'none';
    fileCountDisplay.style.display = 'none';
    multipleImagesInput.value = '';
}

// Clear single preview
function clearSinglePreview() {
    const singleImagePreview = document.getElementById('singleImagePreview');
    const singleImageInput = document.getElementById('image');

    singleImagePreview.style.display = 'none';
    singleImageInput.value = '';
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const multipleMode = document.getElementById('multiple_mode').checked;
    const singleImageInput = document.getElementById('image');
    const multipleImagesInput = document.getElementById('images');

    if (multipleMode) {
        if (!multipleImagesInput.files || multipleImagesInput.files.length === 0) {
            e.preventDefault();
            alert('กรุณาเลือกรูปภาพอย่างน้อย 1 รูป');
            multipleImagesInput.focus();
            return false;
        }

        // ตรวจสอบขนาดไฟล์
        for (let i = 0; i < multipleImagesInput.files.length; i++) {
            if (multipleImagesInput.files[i].size > 5 * 1024 * 1024) {
                e.preventDefault();
                alert(`รูปที่ ${i + 1} มีขนาดเกิน 5MB กรุณาเลือกรูปใหม่`);
                return false;
            }
        }
    } else {
        if (!singleImageInput.files || singleImageInput.files.length === 0) {
            e.preventDefault();
            alert('กรุณาเลือกรูปภาพ');
            singleImageInput.focus();
            return false;
        }

        // ตรวจสอบขนาดไฟล์
        if (singleImageInput.files[0].size > 5 * 1024 * 1024) {
            e.preventDefault();
            alert('รูปภาพมีขนาดเกิน 5MB กรุณาเลือกรูปใหม่');
            return false;
        }
    }
});
</script>
@endsection
