<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ActivityImage;

use Illuminate\Http\Request;

class GalleryController extends Controller
{
    public function index(Request $request)
    {
        $query = ActivityImage::published()->ordered();

        $images = $query->paginate(12)->appends($request->query());

        return view('gallery.index', compact('images'));
    }




}
