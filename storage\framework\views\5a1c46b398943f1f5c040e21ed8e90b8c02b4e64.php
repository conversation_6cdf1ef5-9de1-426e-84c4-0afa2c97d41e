<?php $__env->startSection('title', 'รูปภาพกิจกรรม - ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .gallery-item {
        position: relative;
        overflow: hidden;
        border-radius: 10px;
        cursor: pointer;
    }

    .gallery-item img {
        width: 100%;
        height: 250px;
        object-fit: cover;
        object-position: center center;
    }

    .gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
        opacity: 0;
        display: flex;
        align-items: flex-end;
        padding: 20px;
    }

    .gallery-info {
        color: white;
        width: 100%;
    }

    .category-filter {
        background: white;
        border-radius: 50px;
        padding: 10px 20px;
        margin: 5px;
        border: 2px solid #e9ecef;
    }

    .category-filter.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<section class="bg-info text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-images me-3"></i>
                    รูปภาพกิจกรรม
                </h1>
                <p class="lead">ชมภาพบรรยากาศกิจกรรมต่างๆ ของศูนย์พัฒนาเด็กเล็ก</p>
            </div>
        </div>


    </div>
</section>

<!-- Gallery Content -->
<section class="py-5">
    <div class="container">
        <?php if($images->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6">
                    <div class="gallery-item" data-bs-toggle="modal" data-bs-target="#imageModal<?php echo e($image->id); ?>">
                        <img src="<?php echo e($image->thumbnail_url ?? $image->image_url); ?>"
                             alt="<?php echo e($image->description ?? 'รูปภาพกิจกรรม'); ?>"
                             class="img-fluid">
                        
                        <div class="gallery-overlay">
                            <div class="gallery-info">
                                <h6 class="mb-1">รูปภาพกิจกรรม</h6>
                                <?php if($image->description): ?>
                                    <p class="mb-1 small"><?php echo e(Str::limit($image->description, 60)); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Image Modal -->
                <div class="modal fade" id="imageModal<?php echo e($image->id); ?>" tabindex="-1">
                    <div class="modal-dialog modal-lg modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">รูปภาพกิจกรรม</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="<?php echo e($image->image_url); ?>"
                                     alt="<?php echo e($image->description ?? 'รูปภาพกิจกรรม'); ?>"
                                     class="img-fluid rounded" style="max-height: 70vh;">

                                <?php if($image->description): ?>
                                    <div class="mt-3">
                                        <p class="text-muted"><?php echo e($image->description); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="modal-footer justify-content-end">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            
            <!-- Pagination -->
            <?php if(method_exists($images, 'links')): ?>
            <div class="d-flex justify-content-center mt-5">
                <?php echo e($images->links('custom.pagination')); ?>

            </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-images fa-5x text-muted mb-4"></i>
                        <h3 class="text-muted">ยังไม่มีรูปภาพกิจกรรม</h3>
                        <p class="text-muted">กรุณาติดตามรูปภาพกิจกรรมในภายหลัง</p>
                        <a href="<?php echo e(route('home')); ?>" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>กลับหน้าหลัก
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\childcenter\resources\views/gallery/index.blade.php ENDPATH**/ ?>