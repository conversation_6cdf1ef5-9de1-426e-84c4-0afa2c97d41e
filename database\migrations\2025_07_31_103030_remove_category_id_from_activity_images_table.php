<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('activity_images', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropColumn('category_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('activity_images', function (Blueprint $table) {
            $table->unsignedBigInteger('category_id')->nullable()->after('thumbnail_path');
            $table->foreign('category_id')->references('id')->on('activity_categories')->onDelete('set null');
        });
    }
};
