<?php $__env->startSection('title', 'จัดการรูปภาพ - ระบบจัดการ'); ?>
<?php $__env->startSection('page-title', 'จัดการรูปภาพกิจกรรม'); ?>

<?php $__env->startSection('content'); ?>
<!-- แสดงข้อความแจ้งเตือน -->
<?php if(session('success')): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php if(session('show_continue_upload')): ?>
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-check-circle me-2"></i>
                <?php echo e(session('success')); ?>

            </div>
            <div>
                <a href="<?php echo e(route('admin.images.create')); ?>" class="btn btn-success btn-sm me-2">
                    <i class="fas fa-plus me-1"></i>อัพโหลดต่อ (ชุดถัดไป)
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
        <div class="mt-2">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                คุณสามารถอัพโหลดรูปภาพเพิ่มเติมได้อีก 20 รูปต่อครั้ง
            </small>
        </div>
    <?php else: ?>
        <i class="fas fa-check-circle me-2"></i>
        <?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    <?php endif; ?>
</div>
<?php endif; ?>

<?php if(session('error')): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo e(session('error')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>



<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">รายการรูปภาพกิจกรรม</h4>
    <div>
        <a href="<?php echo e(route('admin.images.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>อัพโหลดรูปภาพ
        </a>
    </div>
</div>



<div class="card">
    <div class="card-body">
        <?php if($images->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="card h-100">
                        <div class="position-relative">
                            <img src="<?php echo e($image->thumbnail_url ?? $image->image_url); ?>"
                                 alt="<?php echo e($image->description ?? 'รูปภาพกิจกรรม'); ?>"
                                 class="card-img-top"
                                 style="height: 200px; object-fit: cover; object-position: center center;">
                            
                            <?php if(!$image->is_published): ?>
                                <span class="position-absolute top-0 start-0 badge bg-warning m-2">
                                    ร่าง
                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-body">
                            <?php if($image->description): ?>
                                <p class="card-text small"><?php echo e(Str::limit($image->description, 50)); ?></p>
                            <?php else: ?>
                                <p class="card-text small text-muted">ไม่มีคำอธิบาย</p>
                            <?php endif; ?>



                            <div class="small text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo e($image->created_at->format('d/m/Y')); ?>

                            </div>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="btn-group w-100" role="group">
                                <a href="<?php echo e(route('admin.images.edit', $image->id)); ?>"
                                   class="btn btn-sm btn-outline-primary"
                                   title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="<?php echo e(route('admin.images.destroy', $image->id)); ?>"
                                      method="POST"
                                      style="display: inline;"
                                      onsubmit="return confirm('คุณต้องการลบรูปภาพนี้หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit"
                                            class="btn btn-sm btn-outline-danger"
                                            title="ลบ">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($images->links('custom.admin-pagination')); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-images fa-5x text-muted mb-4"></i>
                <h5 class="text-muted">ยังไม่มีรูปภาพกิจกรรม</h5>
                <p class="text-muted">เริ่มต้นอัพโหลดรูปภาพแรกของคุณ</p>
                <a href="<?php echo e(route('admin.images.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>อัพโหลดรูปภาพ
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    /* ========== Toggle Switch Styling ========== */
    .form-check-input:checked {
        background-color: #00d084 !important;
        border-color: #00d084 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 208, 132, 0.25) !important;
    }

    .form-check-input:focus {
        border-color: #80bdff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }

    .form-check-input {
        width: 3.5rem !important;
        height: 1.8rem !important;
        border-radius: 1rem !important;
        background-color: #dee2e6 !important;
        border: 2px solid #dee2e6 !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
    }

    .form-check-input:checked::before {
        transform: translateX(1.7rem) !important;
    }

    .status-text {
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        margin-left: 0.5rem !important;
    }

    .form-check {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\childcenter\resources\views/admin/images/index.blade.php ENDPATH**/ ?>